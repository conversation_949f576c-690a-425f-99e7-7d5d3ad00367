import { FoodSafetyNormalRoutShopInfoDTO } from '@src/http/service/tactics/supervisorTask';
import Icon from '@src/ui/components/Icon';
import { Text, View } from 'react-native';

interface MapInfoProps {
  activeShopInfo?: FoodSafetyNormalRoutShopInfoDTO;
}

export const MapInfo = ({ activeShopInfo }: MapInfoProps) => {
  return (
    <View className="flex-col gap-y-1 px-3 py-2">
      <Text className="text-display-2xs font-medium text-[#1D2129]">门店类型说明</Text>
      <View className="flex-row gap-x-4">
        <View className="flex-row items-center gap-x-1">
          <Icon name="not-audited" />
          <Text className="text-display-2xs text-[#86909C]">未稽核门店</Text>
        </View>
        <View className="flex-row items-center gap-x-1">
          <Icon name="audited" />
          <Text className="text-display-2xs text-[#86909C]">已稽核门店</Text>
        </View>
        <View className="flex-row items-center gap-x-1">
          <Icon name="mid" className="fill-white" />
          <Text className="text-display-2xs text-[#86909C]">新增门店</Text>
        </View>
      </View>
      {activeShopInfo && (
        <View className="mt-2 border-t-[0.5px] border-[#F0F0F0] pt-2">
          <Text className="text-display-2xs font-medium text-[#1D2129]">选中门店信息</Text>
          <View className="mt-1 flex-row">
            <View className="w-2/5 flex-col">
              <Text className="text-display-2xs text-[#86909C]">门店编号：{activeShopInfo?.shopId}</Text>
              <Text className="text-display-2xs text-[#86909C]">路线编号：{activeShopInfo?.sort}</Text>
            </View>
            <View>
              <Text className="text-display-2xs text-[#86909C]">门店名称：{activeShopInfo?.shopName}</Text>
              <Text className="text-display-2xs text-[#86909C]">
                坐标：{activeShopInfo?.longitude}，{activeShopInfo?.latitude}
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

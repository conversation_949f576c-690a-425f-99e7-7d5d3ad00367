import { useRequest } from 'ahooks';
import { Timeline } from 'antd';
import dayjs from 'dayjs';
import { getAuditRouteTaskProcess } from '@/http/apis/task-center';
import createModal from '@/utils/antd/createModal';

interface OprModalProps {
  taskId: number;
}

export const OprModal = createModal<{}, OprModalProps>(
  (_p, setVisible, payload) => {
    const { taskId } = payload?.data || {};

    const { data } = useRequest(() => getAuditRouteTaskProcess(taskId), {
      refreshDeps: [taskId],
      ready: !!taskId && !!_p.visible,
    }) as any;

    return [
      {
        footer: null,
      },
      <div className="pt-2">
        <Timeline
          items={data?.map((item) => {
            const _parseParams = JSON.parse(item?.params || '{}');

            console.log('💀 ~ _parseParams:', _parseParams);

            return {
              dot: item?.operatorName ? (
                <div className="py-1 px-2 text-xs rounded-full bg-[#1890FF] text-white">
                  {item?.operatorName?.charAt(0)}
                </div>
              ) : undefined,
              children: (
                <div className="flex flex-col gap-2">
                  <div className="flex justify-between">
                    <div className="flex gap-2">
                      <span>{item?.operatorName}</span>
                      <span>修改任务执行信息</span>
                    </div>
                    <div>{item?.updateTime ? dayjs(item?.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
                  </div>
                  <div className="rounded-lg bg-[#F7F8FA] p-2 flex flex-col">
                    {item?.remark && (
                      <span className="text-sm leading-[22px] text-[#4E5969] break-all">
                        <span className="text-sm font-medium leading-[22px] text-[#5B6A91]">修改原因：</span>
                        {item?.remark}
                      </span>
                    )}
                    {_parseParams?.taskUserName && _parseParams?.taskNewUserName && (
                      <span className="text-sm leading-[22px] text-[#4E5969] break-all">
                        <span className="text-sm font-medium leading-[22px] text-[#5B6A91]">执行人：</span>由{' '}
                        {_parseParams?.taskUserName} 修改为 {_parseParams?.taskNewUserName}
                      </span>
                    )}
                    {_parseParams?.taskBeginTime &&
                      _parseParams?.taskExpiredTime &&
                      _parseParams?.taskNewBeginTime &&
                      _parseParams?.taskNewExpiredTime && (
                        <span className="text-sm leading-[22px] text-[#4E5969] break-all">
                          <span className="text-sm font-medium leading-[22px] text-[#5B6A91]">执行时段：</span>由{' '}
                          {dayjs(_parseParams?.taskBeginTime).format('YYYY-MM-')} 修改为 {_parseParams?.taskNewUserName}
                        </span>
                      )}
                  </div>
                </div>
              ),
            };
          })}
        />
      </div>,
    ];
  },
  {
    title: '操作记录',
    destroyOnClose: true,
  },
);

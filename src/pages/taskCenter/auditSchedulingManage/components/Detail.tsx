/* eslint-disable max-lines-per-function */
import { Key, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { But<PERSON>, Space } from 'antd';
import dayjs from 'dayjs';
import { isEmpty, isNil } from 'lodash';
import { CheckRouteButton } from './CheckRouteButton';
import { ModifyBatchModalForm } from './ModifyBatchModalForm';
import { OprModal } from './OprModal';
import useTaskDetailModal from '../../list/detail/hooks/use-task-detail';
import { descStatusEnum, descStatusEnumCN } from '../const';
import Service, { RequestName } from '../service';
import { getColumns, getCommonConfig, getPersonColumn, getStoreColumn } from '@/components/pro-table-config';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { parserParams } from '@/utils/convert';
import { formatDateToUTC } from '@/utils/date';
import eventEmitter from '@/utils/eventEmitter';
import { PaginationParams } from '@/utils/pagination';

export const Detail = () => {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _detailUrlParams = parserParams(urlParams?.detail || {});

  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const { showModal } = useTaskDetailModal();

  const [service, executeRequest] = Service();

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });

  const getRouteIdsByBatchDate = (date?: string) => {
    executeRequest(RequestName.GetRouteIdsByBatchDate, date);
  };

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        {
          dataIndex: 'processUserIds',
          title: '执行人',
          colSize: 1.5,
          search: {
            transform: (value: any) => {
              return {
                processUserIds: Array.isArray(value) ? value : [value],
              };
            },
          },
        },
      ),
      {
        title: '人工是否有调整',
        dataIndex: 'isModify',
        valueType: 'select',
        valueEnum: {
          true: '是',
          false: '否',
        },
        colSize: 1.5,
      },
      {
        title: '路线ID',
        dataIndex: 'foodSafetyNormalRouteBatchId',
        valueType: 'select',
        fieldProps: {
          options: service?.routeIds,
          showSearch: true,
        },
        colSize: 1.5,
      },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        valueType: 'select',
        valueEnum: descStatusEnumCN,
        colSize: 1.5,
        search: {
          transform: (value: any) => {
            return {
              taskStatus: Array.isArray(value) ? value : [value],
            };
          },
        },
        fieldProps: {
          options: [
            { label: '待开始', value: descStatusEnum.WAITING_START },
            { label: '进行中', value: descStatusEnum.RUNNING },
            { label: '已完成', value: descStatusEnum.COMPLETED },
            { label: '已作废', value: descStatusEnum.CANCELED },
            { label: '已逾期', value: descStatusEnum.EXPIRED },
            { label: '逾期进行中', value: descStatusEnum.EXPIRED_RUNNING },
          ],
        },
      },
      {
        title: '任务提交时间',
        key: 'submitTime',
        valueType: 'dateRange',
        search: {
          transform: (value: any) => {
            return {
              taskSubmitBeginTime: formatDateToUTC(dayjs(value[0]).startOf('day')),
              taskSubmitEndTime: formatDateToUTC(dayjs(value[1]).endOf('day')),
            };
          },
        },
        colSize: 2,
      },
    ],
    tableColumns: [
      {
        title: '路线ID',
        dataIndex: 'batchId',
        width: 300,
      },
      {
        title: '门店id',
        dataIndex: 'shopId',
        width: 150,
      },
      {
        title: '门店名称',
        dataIndex: 'shopName',
        width: 250,
      },
      {
        title: '稽核人员',
        dataIndex: 'processUserName',
        width: 200,
      },
      {
        title: '任务类型',
        key: 'taskSubType',
        width: 200,
        render: () => {
          return '食安线下稽核';
        },
      },
      {
        title: '路径预览',
        key: 'preview',
        render: (_, record) => {
          return <CheckRouteButton batchId={record?.batchId} />;
        },
        width: 150,
      },
      {
        title: '任务执行时段',
        key: 'time',
        width: 230,
        render: (_, record) => {
          return record?.taskBeginTime && record?.taskExpiredTime ? (
            <div className="flex flex-col shrink-0">
              <span>{dayjs(record?.taskBeginTime).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span>{dayjs(record?.taskExpiredTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          ) : (
            '-'
          );
        },
      },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        valueEnum: descStatusEnumCN,
        width: 150,
      },
      {
        title: '任务提交时间',
        dataIndex: 'taskSubmitTime',
        width: 230,
        render: (_, record) => {
          return record?.taskSubmitTime ? dayjs(record?.taskSubmitTime).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
      {
        title: '操作',
        valueType: 'option',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              {/* 待开始 && taskId不为空 */}
              {record?.taskStatus === descStatusEnum.WAITING_START && record?.taskId && (
                <Button
                  size="small"
                  type="link"
                  onClick={() => {
                    ModifyBatchModalForm.showModal({
                      data: {
                        isInfoModal: true,
                        ids: [record?.id],
                        onSuccess: () => {
                          actionRef.current.reload();
                          setSelectKeys([]);
                          setSelectedRows([]);
                        },
                      },
                    });
                  }}
                >
                  编辑
                </Button>
              )}
              {record?.isModify && (
                <Button
                  size="small"
                  type="link"
                  onClick={() => {
                    OprModal.showModal({
                      data: {
                        taskId: record?.taskId,
                      },
                    });
                  }}
                >
                  操作记录
                </Button>
              )}
            </Space>
          );
        },
      },
    ],
  });

  const formatFormValue = (value: any) => {
    const { store, ...rest } = value;

    return { ...store, ...rest };
  };

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    getRouteIdsByBatchDate();

    if (!isEmpty(_detailUrlParams)) {
      const { store, isModify, taskSubmitBeginTime, taskSubmitEndTime, ...rest } = _detailUrlParams;
      const { groupId, shopIds } = store || {};

      if (_detailUrlParams?.processUserIds) {
        executeRequest(RequestName.GetUserInfoList);
      }

      if (shopIds?.length) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }

      form.setFieldsValue({
        ...rest,
        store,
        isModify: !isNil(isModify) ? isModify?.toString() : undefined,
        submitTime: taskSubmitBeginTime && taskSubmitEndTime ? [taskSubmitBeginTime, taskSubmitEndTime] : undefined,
      });

      setPaginationParams({
        pageNo: _detailUrlParams?.pageNo || 1,
        pageSize: _detailUrlParams?.pageSize || 10,
      });
    }

    form.submit();

    eventEmitter.on('GET_SCHEDULINGDESC_WITH_BATCHID', ({ foodSafetyNormalRouteBatchId }) => {
      form.setFieldsValue({ foodSafetyNormalRouteBatchId });

      form.submit();
    });

    return () => {
      eventEmitter.off('GET_SCHEDULINGDESC_WITH_BATCHID');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ProTable
        {...getCommonConfig({
          search: {
            form,
          },
        })}
        scroll={{ x: 2000 }}
        actionRef={actionRef}
        rowKey="id"
        options={false}
        columns={columns}
        pagination={{
          showSizeChanger: true,
          current: paginationParams.pageNo,
          pageSize: paginationParams.pageSize,
          onChange: (pageNo, pageSize) => {
            setPaginationParams({ pageNo, pageSize });
          },
        }}
        tableExtraRender={() => (
          <ProCard>
            <Button
              type="primary"
              disabled={!selectKeys?.length}
              onClick={() => {
                ModifyBatchModalForm.showModal({
                  data: {
                    isInfoModal: true,
                    ids: selectKeys,
                    onSuccess: () => {
                      actionRef.current.reload();
                      setSelectKeys([]);
                      setSelectedRows([]);
                    },
                  },
                });
              }}
            >
              修改任务执行信息
            </Button>
          </ProCard>
        )}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        manualRequest={true}
        request={async (value) => {
          const { current, pageSize, ...val } = value;

          const payload = {
            ...formatFormValue(val),
            pageNo: current || _detailUrlParams?.current || 1,
            pageSize: pageSize || _detailUrlParams?.pageSize || 10,
          };

          const _urlParams = {
            ...val,
            pageNo: current || _detailUrlParams?.current || 1,
            pageSize: pageSize || _detailUrlParams?.pageSize || 10,
          };

          setUrlParams({
            ...urlParams,
            detail: _urlParams,
          });

          setPaginationParams({
            pageNo: current || _detailUrlParams?.current || 1,
            pageSize: pageSize || _detailUrlParams?.pageSize || 10,
          });

          const res = await executeRequest(RequestName.GetAuditRouteDataPageList, payload);

          return {
            data: res?.data || [],
            success: true,
            total: res?.total,
          };
        }}
        rowSelection={{
          selectedRowKeys: selectKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
          renderCell: (_, record, index, originNode) => {
            // 待开始或者无状态
            if (record?.taskStatus === descStatusEnum.WAITING_START && record?.taskId) {
              return originNode;
            }

            return null;
          },
          getCheckboxProps: (record) => ({
            disabled: !(record?.taskStatus === descStatusEnum.WAITING_START && record?.taskId),
          }),
        }}
      />
      <ModifyBatchModalForm />
      <OprModal />
    </>
  );
};
